'use client';

import Link from 'next/link';
import { useState, useEffect, useRef, MouseEvent } from 'react';
import { usePathname } from 'next/navigation';
import { Home, Settings, Phone, Info, LucideIcon } from 'lucide-react';
import { FC } from 'react';

interface NavItem {
  href: string;
  icon: LucideIcon;
  alt: string;
  label: string;
}

const Navbar: FC = () => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const [showHamburger, setShowHamburger] = useState<boolean>(true);
  const pathname = usePathname();
  const navRef = useRef<HTMLDivElement | null>(null);

  // Add effect to handle body margin when navbar expands (only on desktop)
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        if (isExpanded) {
          document.body.style.marginLeft = '200px'; // Reduced from 256px
          document.body.style.transition = 'margin-left 0.3s ease-in-out';
        } else {
          document.body.style.marginLeft = '60px'; // Reduced from 96px
          document.body.style.transition = 'margin-left 0.3s ease-in-out';
        }
      } else {
        // Reset margin on mobile/tablet
        document.body.style.marginLeft = '0px';
        document.body.style.transition = 'margin-left 0.3s ease-in-out';
      }
    };

    handleResize(); // Call immediately
    window.addEventListener('resize', handleResize);

    // Cleanup function to reset margin when component unmounts
    return () => {
      document.body.style.marginLeft = '0px';
      document.body.style.transition = '';
      window.removeEventListener('resize', handleResize);
    };
  }, [isExpanded]);

  // Determine hamburger color based on route
  const isBlackHamburger = ['/about', '/services', '/contact'].includes(pathname);

  const navItems: NavItem[] = [
    { href: '/', icon: Home, alt: 'Home', label: 'HOME' },
    { href: '/about', icon: Info, alt: 'About', label: 'ABOUT US' },
    { href: '/services', icon: Settings, alt: 'Services', label: 'SERVICES' },
    { href: '/contact', icon: Phone, alt: 'Contact', label: 'CONTACT US' },
  ];

  const toggleNavbar = () => {
    setIsExpanded(prev => !prev);
  };

  // Close navbar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | globalThis.MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node) && isExpanded) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    // Clean up in case component unmounts while menu is open
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMobileMenuOpen]);

  // Hide hamburger icon on scroll (show only at top)
  useEffect(() => {
    const handleScroll = () => {
      setShowHamburger(window.scrollY === 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className="fixed top-0 left-0 w-full z-50" role="navigation" aria-label="Main navigation">
      {/* Hamburger for mobile (always rendered for smooth animation) */}
      {showHamburger && (
        <button
          className="lg:hidden fixed top-6 right-6 z-[100] flex items-center focus:outline-none"
          aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
          onClick={() => setIsMobileMenuOpen(prev => !prev)}
          style={{ width: '40px', height: '40px' }}
        >
          <span className="relative w-6 h-6 block">
            {/* Top bar */}
            <span
              className={`absolute left-0 top-1/2 w-6 h-0.5 rounded transition-all duration-300 transform ${(isMobileMenuOpen ? 'bg-white' : (isBlackHamburger ? 'bg-black' : 'bg-white'))} ${isMobileMenuOpen ? 'rotate-45' : '-translate-y-2.5'}`}
              style={{ transitionProperty: 'transform, background-color, opacity' }}
            ></span>
            {/* Middle bar */}
            <span
              className={`absolute left-0 top-1/2 w-6 h-0.5 rounded transition-all duration-300 transform ${(isMobileMenuOpen ? 'bg-white' : (isBlackHamburger ? 'bg-black' : 'bg-white'))} ${isMobileMenuOpen ? 'opacity-0' : 'opacity-100'}`}
              style={{ transitionProperty: 'opacity, background-color' }}
            ></span>
            {/* Bottom bar */}
            <span
              className={`absolute left-0 top-1/2 w-6 h-0.5 rounded transition-all duration-300 transform ${(isMobileMenuOpen ? 'bg-white' : (isBlackHamburger ? 'bg-black' : 'bg-white'))} ${isMobileMenuOpen ? '-rotate-45' : 'translate-y-2.5'}`}
              style={{ transitionProperty: 'transform, background-color, opacity' }}
            ></span>
          </span>
        </button>
      )}

      {/* Mobile Overlay Menu */}
      <div
        className={`lg:hidden fixed inset-0 bg-black z-[99] flex flex-col items-center justify-center transition-all duration-300 ease-in-out
          ${isMobileMenuOpen ? 'translate-x-0 opacity-100 pointer-events-auto' : 'translate-x-full opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col gap-8 mt-16">
          <Link href="/" className="text-lg font-medium text-white text-center" onClick={() => setIsMobileMenuOpen(false)}>Home</Link>
          <Link href="/about" className="text-lg font-medium text-white text-center" onClick={() => setIsMobileMenuOpen(false)}>About Us</Link>
          <Link href="/services" className="text-lg font-medium text-white text-center" onClick={() => setIsMobileMenuOpen(false)}>Services</Link>
          <Link href="/contact" className="text-lg font-medium text-white text-center" onClick={() => setIsMobileMenuOpen(false)}>Contact Us</Link>
        </div>
      </div>

      {/* Sidebar for desktop */}
      <div
        ref={navRef}
        className={`hidden lg:flex fixed top-0 left-0 h-screen bg-black z-50 flex-col items-center justify-center gap-6 py-8 shadow-lg transition-all ease-in-out duration-300 ${
          isExpanded ? 'w-48' : 'w-16'
        }`}
        style={{ transitionProperty: 'width' }}
        onMouseEnter={() => setIsExpanded(true)}
        onMouseLeave={() => setIsExpanded(false)}
      >
        {navItems.map((item, index) => {
          const isActive = pathname === item.href;
          const IconComponent = item.icon;

          return (
            <Link
              key={index}
              href={item.href}
              className={`group relative transition-all duration-300 flex items-center w-full ${
                isActive ? 'opacity-100' : 'opacity-70 hover:opacity-90'
              } ${isExpanded ? 'justify-start px-8' : 'justify-center'}`}
              aria-label={item.label}
            >
              <IconComponent
                size={20}
                className={`transition-colors duration-300 flex-shrink-0 ${
                  isActive ? 'text-[#05A0E2]' : 'text-white'
                }`}
              />

              {isExpanded && (
                <span
                  className={`ml-4 text-sm font-medium tracking-wider whitespace-nowrap transition-colors duration-300 ${
                    isActive ? 'text-[#05A0E2]' : 'text-white'
                  }`}
                >
                  {item.label}
                </span>
              )}

              {!isExpanded && (
                <div className="absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
                  {item.label}
                </div>
              )}
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

export default Navbar;