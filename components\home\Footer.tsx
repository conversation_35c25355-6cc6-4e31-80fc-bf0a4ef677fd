'use client';

import Link from 'next/link';
import { Mail, Phone, MapPin } from 'lucide-react';
import Image from 'next/image';
import linkedinicon from '../icons/LinkedIn';

const Footer = () => {
  const socialIcons = {
    linkedin: linkedinicon,
  };

  return (
    <footer className="relative bg-[#F9FAFB] text-gray-700 py-12 overflow-hidden">
      {/* Main Content - responsive padding */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10 lg:gap-12 relative z-20 lg:ml-20">
          {/* Logo & Description */}
          <div className="flex flex-col items-center lg:items-start text-center lg:text-left space-y-4 w-full mb-8 lg:mb-0">
            <Image src="/logo-F.png" alt="Prolytech Logo" width={140} height={30} />
            <p className="text-[12px] text-gray-400 leading-[18px] font-extralight max-w-xs mx-auto lg:mx-0" style={{ fontFamily: 'Inter' }}>
              Building secure, cloud-native, and AI-powered digital products at scale. We help brands, communities, and platforms bring bold ideas to life.
            </p>
            <div className="flex gap-3 pt-2 justify-center lg:justify-start w-full">
              {Object.entries(socialIcons).map(([platform, IconComponent]) => (
                <a
                  href={platform === 'linkedin' ? 'https://www.linkedin.com/company/prolytech-solutions-pvt-ltd/' : '#'}
                  key={platform}
                  aria-label={platform}
                  className="hover:opacity-80 transition-opacity"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <div className="bg-[#E6F0FF] rounded-full p-1 flex items-center justify-center">
                    <IconComponent className="h-4 w-4" />
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Pages */}
          <div className="flex flex-col items-center lg:items-start text-center lg:text-left mt-0 ml-0 sm:ml-8 lg:ml-16 w-full mb-8 lg:mb-0">
            <h4 className="font-bold text-[11px] uppercase mb-3 text-gray-800 tracking-wider">Pages</h4>
            <ul className="space-y-2 text-[13px] text-gray-500">
              <li><Link href="/" className="hover:text-blue-500 transition-colors">Home</Link></li>
              <li><Link href="/about" className="hover:text-blue-500 transition-colors">About Us</Link></li>
              <li><Link href="/services" className="hover:text-blue-500 transition-colors">Services</Link></li>
              <li><Link href="/contact" className="hover:text-blue-500 transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Services */}
          <div className="flex flex-col items-center lg:items-start text-center lg:text-left mt-0 w-full mb-8 lg:mb-0">
            <h4 className="font-bold text-[11px] uppercase mb-3 text-gray-800 tracking-wider">Services</h4>
            <ul className="space-y-2 text-[13px] text-gray-500">
              <li><Link href="/services#custom-dev" className="hover:text-blue-500 transition-colors">Custom Software Development</Link></li>
              <li><Link href="/services#cloud" className="hover:text-blue-500 transition-colors">Cloud & DevOps</Link></li>
              <li><Link href="/services#ai" className="hover:text-blue-500 transition-colors">AI & Automation</Link></li>
              <li><Link href="/services#cyber" className="hover:text-blue-500 transition-colors">Cybersecurity & Compliance</Link></li>
              <li><Link href="/services#product" className="hover:text-blue-500 transition-colors">Product Engineering</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="flex flex-col items-center lg:items-start text-center lg:text-left mt-0 w-full">
            <h4 className="font-bold text-[11px] uppercase mb-3 text-gray-800 tracking-wider">Contact</h4>
            <ul className="space-y-4 text-[13px] text-gray-500">
              <li className="flex items-center justify-center lg:justify-start gap-2">
                <Mail size={16} className="text-blue-500 flex-shrink-0" />
                <span className="break-all text-gray-600 text-[13px]"><EMAIL></span>
              </li>
              <li className="flex items-center justify-center lg:justify-start gap-2">
                <Phone size={16} className="text-blue-500 flex-shrink-0" />
                <span className="text-gray-600 text-[13px]">+91-8247532770</span>
              </li>
              <li className="flex items-center justify-center lg:justify-start gap-2">
                <MapPin size={16} className="text-blue-500 flex-shrink-0" />
                <span className="text-gray-600 text-[13px]">Hyderabad, India</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-200 text-[11px] text-gray-400 text-center mt-12 pt-4 max-w-7xl mx-auto relative z-20 px-4 sm:px-6 lg:px-8">
        <p>© 2025 Prolytech Solutions. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;
